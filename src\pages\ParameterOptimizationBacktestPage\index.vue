<template>
  <div class="parameter-optimization-backtest-page">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <span>参数优化回测</span>
        </div>
      </template>

      <!-- 参数输入表单 -->
      <el-form :model="form" label-width="140px" class="backtest-form">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="股票代码">
              <el-input
                v-model="form.stockCodesText"
                type="textarea"
                :rows="3"
                placeholder="请输入股票代码，多个代码用逗号、分号或换行分隔，例如: 000001.SZ,000002.SZ,600000.SH"
                @input="parseStockCodes"
              ></el-input>
              <div class="stock-codes-preview" v-if="stockCodes.length > 0">
                <el-tag
                  v-for="code in stockCodes"
                  :key="code"
                  closable
                  @close="removeStockCode(code)"
                  style="margin: 2px;"
                >
                  {{ code }}
                </el-tag>
                <div class="codes-count">共 {{ stockCodes.length }} 只股票</div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始日期">
              <el-date-picker v-model="form.startDate" type="date" placeholder="选择开始日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束日期">
              <el-date-picker v-model="form.endDate" type="date" placeholder="选择结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="初始资金">
              <el-input-number v-model="form.initialCapital" :min="1000" :step="10000" placeholder="例如: 100000"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 参数优化配置 -->
        <el-divider content-position="left">参数优化配置</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="短周期最小值">
              <el-input-number v-model="form.shortPeriodMin" :min="1" placeholder="例如: 2"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="短周期最大值">
              <el-input-number v-model="form.shortPeriodMax" :min="1" placeholder="例如: 10"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="短周期步长">
              <el-input-number v-model="form.shortPeriodStep" :min="1" placeholder="例如: 2"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="长周期最小值">
              <el-input-number v-model="form.longPeriodMin" :min="1" placeholder="例如: 10"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="长周期最大值">
              <el-input-number v-model="form.longPeriodMax" :min="1" placeholder="例如: 50"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="长周期步长">
              <el-input-number v-model="form.longPeriodStep" :min="1" placeholder="例如: 10"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="每次交易股数">
              <el-input-number v-model="form.sharesPerTrade" :min="100" :step="100" placeholder="例如: 100"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item>
              <div class="parameter-preview">
                <span class="preview-label">参数组合预览：</span>
                <span class="preview-text">{{ parameterCombinationsPreview }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="runParameterOptimization" :loading="loading">
                {{ loading ? `执行中 (${progress.current}/${progress.total})` : '开始参数优化' }}
              </el-button>
              <el-button @click="resetForm">重置参数</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 执行进度 -->
    <div v-if="loading" class="loading-section">
      <el-card>
        <template #header>执行进度</template>
        <div v-if="progress.total > 0">
          <el-progress
            :percentage="Math.round((progress.current / progress.total) * 100)"
            :status="progress.error ? 'exception' : 'success'"
          />
          <p style="margin-top: 10px;">
            正在处理: {{ progress.currentStock }} - 短周期{{ progress.currentShortPeriod }}/长周期{{ progress.currentLongPeriod }} 
            ({{ progress.current }}/{{ progress.total }})
          </p>
        </div>
        <el-skeleton :rows="5" animated />
      </el-card>
    </div>

    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon :closable="false"></el-alert>
    </div>

    <!-- 优化结果展示 -->
    <div v-if="optimizationResults.length > 0 && !loading && !error" class="optimization-results-section">
      <el-card class="summary-card section-card">
        <template #header>
          <div class="card-header-with-actions">
            <span>参数优化结果</span>
            <div class="header-actions">
              <el-button size="small" @click="exportOptimizationResults">导出结果</el-button>
              <el-button size="small" @click="showOptimizationChart">优化图表</el-button>
            </div>
          </div>
        </template>

        <!-- 筛选工具栏 -->
        <div class="filter-toolbar">
          <div class="filter-section">
            <span class="filter-label">排序方式：</span>
            <el-select v-model="sortBy" @change="handleSortChange" size="small" style="width: 150px;">
              <el-option label="总收益率" value="return_rate"></el-option>
              <el-option label="年化收益率" value="annualized_return"></el-option>
              <el-option label="夏普比率" value="sharpe_ratio"></el-option>
              <el-option label="最大回撤" value="max_drawdown"></el-option>
              <el-option label="胜率" value="win_rate"></el-option>
            </el-select>
            <el-radio-group v-model="sortOrder" @change="handleSortChange" size="small" style="margin-left: 10px;">
              <el-radio-button label="desc">降序</el-radio-button>
              <el-radio-button label="asc">升序</el-radio-button>
            </el-radio-group>
          </div>
          <div class="filter-stats">
            <el-tag type="info" size="small">总组合数: {{ optimizationResults.length }}</el-tag>
            <el-tag type="success" size="small">成功组合: {{ successfulCombinations }}</el-tag>
          </div>
        </div>

        <el-table :data="sortedOptimizationResults" stripe border max-height="600px" @row-click="selectOptimizationResult">
          <el-table-column prop="stock_code" label="股票代码" width="120" sortable></el-table-column>
          <el-table-column prop="short_period" label="短周期" width="80" sortable></el-table-column>
          <el-table-column prop="long_period" label="长周期" width="80" sortable></el-table-column>
          <el-table-column prop="return_rate" label="总收益率" width="120" sortable :formatter="cellFormatPercentage">
            <template #default="scope">
              <span :class="scope.row.return_rate >= 0 ? 'profit' : 'loss'">
                {{ formatPercentage(scope.row.return_rate) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="annualized_return" label="年化收益率" width="130" sortable :formatter="cellFormatPercentage">
            <template #default="scope">
              <span :class="scope.row.annualized_return >= 0 ? 'profit' : 'loss'">
                {{ formatPercentage(scope.row.annualized_return) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="max_drawdown" label="最大回撤" width="120" sortable :formatter="cellFormatPercentage"></el-table-column>
          <el-table-column prop="sharpe_ratio" label="夏普比率" width="120" sortable>
            <template #default="scope">
              {{ scope.row.sharpe_ratio ? scope.row.sharpe_ratio.toFixed(3) : 'N/A' }}
            </template>
          </el-table-column>
          <el-table-column prop="win_rate" label="胜率" width="100" sortable :formatter="cellFormatPercentage"></el-table-column>
          <el-table-column prop="transaction_count" label="交易次数" width="100" sortable></el-table-column>
          <el-table-column prop="final_value" label="最终价值" width="130" sortable :formatter="cellFormatCurrency"></el-table-column>
          <el-table-column label="操作" width="160" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" @click.stop="viewOptimizationDetails(scope.row)">详情</el-button>
              <el-button size="small" type="success" @click.stop="analyzeOptimizationStock(scope.row)" style="margin-left: 5px;">分析</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 选中的优化结果详情 -->
    <div v-if="selectedOptimizationResult && !loading && !error" class="selected-result-section">
      <el-row :gutter="20">
        <!-- 左侧：核心指标 -->
        <el-col :span="16">
          <el-card class="summary-card section-card">
            <template #header>优化结果详情 - {{ selectedOptimizationResult.stock_code }} (短周期{{ selectedOptimizationResult.short_period }}/长周期{{ selectedOptimizationResult.long_period }})</template>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="股票代码">{{ selectedOptimizationResult.stock_code }}</el-descriptions-item>
              <el-descriptions-item label="短周期">{{ selectedOptimizationResult.short_period }}</el-descriptions-item>
              <el-descriptions-item label="长周期">{{ selectedOptimizationResult.long_period }}</el-descriptions-item>
              
              <el-descriptions-item label="回测周期">{{ selectedOptimizationResult.start_date }} 至 {{ selectedOptimizationResult.end_date }}</el-descriptions-item>
              <el-descriptions-item label="初始资金">{{ formatCurrency(selectedOptimizationResult.initial_capital) }}</el-descriptions-item>
              <el-descriptions-item label="最终价值">{{ formatCurrency(selectedOptimizationResult.final_value) }}</el-descriptions-item>
              
              <el-descriptions-item label="总收益额">{{ formatCurrency(selectedOptimizationResult.total_return) }}</el-descriptions-item>
              <el-descriptions-item label="总收益率">{{ formatPercentage(selectedOptimizationResult.return_rate) }}</el-descriptions-item>
              <el-descriptions-item label="年化收益率">{{ formatPercentage(selectedOptimizationResult.annualized_return) }}</el-descriptions-item>

              <el-descriptions-item label="最大回撤">{{ formatPercentage(selectedOptimizationResult.max_drawdown) }}</el-descriptions-item>
              <el-descriptions-item label="胜率">{{ formatPercentage(selectedOptimizationResult.win_rate) }}</el-descriptions-item>
              <el-descriptions-item label="夏普比率">{{ selectedOptimizationResult.sharpe_ratio ? selectedOptimizationResult.sharpe_ratio.toFixed(3) : 'N/A' }}</el-descriptions-item>
              
              <el-descriptions-item label="交易总次数">{{ selectedOptimizationResult.transaction_count }}</el-descriptions-item>
              <el-descriptions-item label="买入次数">{{ selectedOptimizationResult.buy_count }}</el-descriptions-item>
              <el-descriptions-item label="卖出次数">{{ selectedOptimizationResult.sell_count }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>

        <!-- 右侧：投资组合价值图表 -->
        <el-col :span="8">
          <el-card class="chart-card section-card">
            <template #header>投资组合价值</template>
            <div ref="optimizationPortfolioChart" style="width: 100%; height: 400px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import axios from 'axios';
import * as echarts from 'echarts';

// API基础路径
const API_BASE_URL = 'http://localhost:8000/api/period_change_pct_stats';

// 路由实例
const router = useRouter();

// Helper function to format date as YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Get today's date and one year ago date
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(today.getFullYear() - 1);

const form = reactive({
  stockCodesText: '600320,002554,000571,000890,600243',
  startDate: formatDate(oneYearAgo),
  endDate: formatDate(today),
  shortPeriodMin: 2,
  shortPeriodMax: 10,
  shortPeriodStep: 2,
  longPeriodMin: 10,
  longPeriodMax: 50,
  longPeriodStep: 10,
  initialCapital: 100000,
  sharesPerTrade: 100,
});

const loading = ref(false);
const error = ref(null);
const optimizationResults = ref([]);
const selectedOptimizationResult = ref(null);
const stockCodes = ref([]);
const optimizationPortfolioChart = ref(null);
let optimizationChartInstance = null;

// 排序相关状态
const sortBy = ref('return_rate');
const sortOrder = ref('desc');

// 进度状态
const progress = reactive({
  current: 0,
  total: 0,
  currentStock: '',
  currentShortPeriod: 0,
  currentLongPeriod: 0,
  error: false
});

// 解析股票代码
const parseStockCodes = () => {
  const text = form.stockCodesText.trim();
  if (!text) {
    stockCodes.value = [];
    return;
  }

  // 支持逗号、分号、换行分隔
  const codes = text
    .split(/[,;，；\n\r]+/)
    .map(code => code.trim().toUpperCase())
    .filter(code => code.length > 0);

  stockCodes.value = [...new Set(codes)]; // 去重
};

// 移除股票代码
const removeStockCode = (codeToRemove) => {
  stockCodes.value = stockCodes.value.filter(code => code !== codeToRemove);
  form.stockCodesText = stockCodes.value.join(',');
};

// 初始化解析股票代码
parseStockCodes();

// 计算参数组合预览
const parameterCombinationsPreview = computed(() => {
  if (!form.shortPeriodMin || !form.shortPeriodMax || !form.shortPeriodStep ||
      !form.longPeriodMin || !form.longPeriodMax || !form.longPeriodStep) {
    return '请完整填写参数范围';
  }

  const shortPeriods = [];
  for (let sp = form.shortPeriodMin; sp <= form.shortPeriodMax; sp += form.shortPeriodStep) {
    shortPeriods.push(sp);
  }

  const longPeriods = [];
  for (let lp = form.longPeriodMin; lp <= form.longPeriodMax; lp += form.longPeriodStep) {
    longPeriods.push(lp);
  }

  let validCombinations = 0;
  shortPeriods.forEach(sp => {
    longPeriods.forEach(lp => {
      if (lp > sp) {
        validCombinations++;
      }
    });
  });

  return `短周期: [${form.shortPeriodMin}-${form.shortPeriodMax}]步长${form.shortPeriodStep}, 长周期: [${form.longPeriodMin}-${form.longPeriodMax}]步长${form.longPeriodStep}, 有效组合: ${validCombinations}个`;
});

// 成功组合数量
const successfulCombinations = computed(() => {
  return optimizationResults.value.filter(result => result.return_rate !== null && result.return_rate !== undefined).length;
});

// 排序后的优化结果
const sortedOptimizationResults = computed(() => {
  const results = [...optimizationResults.value];
  return results.sort((a, b) => {
    const aValue = a[sortBy.value] || 0;
    const bValue = b[sortBy.value] || 0;

    if (sortOrder.value === 'desc') {
      return bValue - aValue;
    } else {
      return aValue - bValue;
    }
  });
});

// 重置表单
const resetForm = () => {
  form.stockCodesText = '600320,002554,000571,000890,600243';
  form.startDate = formatDate(oneYearAgo);
  form.endDate = formatDate(today);
  form.shortPeriodMin = 2;
  form.shortPeriodMax = 10;
  form.shortPeriodStep = 2;
  form.longPeriodMin = 10;
  form.longPeriodMax = 50;
  form.longPeriodStep = 10;
  form.initialCapital = 100000;
  form.sharesPerTrade = 100;
  optimizationResults.value = [];
  selectedOptimizationResult.value = null;
  stockCodes.value = [];
  error.value = null;
  parseStockCodes();
};

// 参数优化主函数
const runParameterOptimization = async () => {
  // 参数验证
  if (!form.startDate || !form.endDate || !form.initialCapital || !form.sharesPerTrade) {
    ElMessage.error('所有参数均为必填项！');
    return;
  }

  if (!form.shortPeriodMin || !form.shortPeriodMax || !form.shortPeriodStep ||
      !form.longPeriodMin || !form.longPeriodMax || !form.longPeriodStep) {
    ElMessage.error('请完整填写参数优化配置！');
    return;
  }

  if (form.shortPeriodMin >= form.shortPeriodMax) {
    ElMessage.error('短周期最小值必须小于最大值！');
    return;
  }

  if (form.longPeriodMin >= form.longPeriodMax) {
    ElMessage.error('长周期最小值必须小于最大值！');
    return;
  }

  if (form.longPeriodMin <= form.shortPeriodMax) {
    ElMessage.error('长周期最小值必须大于短周期最大值！');
    return;
  }

  if (stockCodes.value.length === 0) {
    ElMessage.error('请至少输入一个股票代码！');
    return;
  }

  loading.value = true;
  error.value = null;
  optimizationResults.value = [];
  selectedOptimizationResult.value = null;

  try {
    // 生成所有有效的参数组合
    const parameterCombinations = [];

    for (let sp = form.shortPeriodMin; sp <= form.shortPeriodMax; sp += form.shortPeriodStep) {
      for (let lp = form.longPeriodMin; lp <= form.longPeriodMax; lp += form.longPeriodStep) {
        if (lp > sp) { // 确保长周期大于短周期
          stockCodes.value.forEach(stockCode => {
            parameterCombinations.push({
              stockCode,
              shortPeriod: sp,
              longPeriod: lp
            });
          });
        }
      }
    }

    // 初始化进度
    progress.current = 0;
    progress.total = parameterCombinations.length;
    progress.error = false;

    const results = [];

    // 执行所有参数组合的回测
    for (let i = 0; i < parameterCombinations.length; i++) {
      const combination = parameterCombinations[i];
      progress.current = i + 1;
      progress.currentStock = combination.stockCode;
      progress.currentShortPeriod = combination.shortPeriod;
      progress.currentLongPeriod = combination.longPeriod;

      try {
        const params = {
          start_date: form.startDate,
          end_date: form.endDate,
          short_period: combination.shortPeriod,
          long_period: combination.longPeriod,
          initial_capital: form.initialCapital,
          shares_per_trade: form.sharesPerTrade,
        };

        const response = await axios.get(`${API_BASE_URL}/backtest/analyze/${combination.stockCode}`, { params });
        if (response.data) {
          // 添加参数信息到结果中
          const resultWithParams = {
            ...response.data,
            short_period: combination.shortPeriod,
            long_period: combination.longPeriod
          };
          results.push(resultWithParams);
        }
      } catch (err) {
        console.error(`股票 ${combination.stockCode} 参数组合 ${combination.shortPeriod}/${combination.longPeriod} 回测失败:`, err);
        // 继续处理下一个组合，不中断整个流程
      }
    }

    optimizationResults.value = results;

    if (results.length === 0) {
      ElMessage.warning('所有参数组合回测都失败了，请检查股票代码和参数');
    } else {
      ElMessage.success(`参数优化完成！成功处理 ${results.length}/${parameterCombinations.length} 个参数组合`);
    }
  } catch (err) {
    console.error("参数优化失败:", err);
    error.value = `参数优化失败: ${err.message || '未知错误'}`;
    ElMessage.error(error.value);
    progress.error = true;
  } finally {
    loading.value = false;
  }
};

// 排序变化处理
const handleSortChange = () => {
  // 排序逻辑已在计算属性中处理
};

// 选择优化结果
const selectOptimizationResult = (row) => {
  selectedOptimizationResult.value = row;
  // 初始化图表
  nextTick(() => {
    if (row.portfolio_values && row.portfolio_values.length > 0) {
      initOptimizationPortfolioChart();
    }
  });
};

// 查看优化详情
const viewOptimizationDetails = (row) => {
  selectOptimizationResult(row);
  // 滚动到详情区域
  nextTick(() => {
    const detailsElement = document.querySelector('.selected-result-section');
    if (detailsElement) {
      detailsElement.scrollIntoView({ behavior: 'smooth' });
    }
  });
};

// 分析优化股票
const analyzeOptimizationStock = (row) => {
  const stockCode = row.stock_code;

  // 构建目标URL，包含股票代码和自动分析标记
  const targetUrl = router.resolve({
    name: 'BatchPeriodChangeStats',
    query: {
      stockCode: stockCode,
      autoAnalyze: 'true'
    }
  });

  // 在新标签页中打开
  window.open(targetUrl.href, '_blank');

  ElMessage.success(`正在新标签页中打开分析页面，股票代码: ${stockCode}`);
};

// 导出优化结果
const exportOptimizationResults = () => {
  if (optimizationResults.value.length === 0) {
    ElMessage.warning('没有可导出的数据');
    return;
  }

  // 简单的CSV导出
  const headers = ['股票代码', '短周期', '长周期', '总收益率(%)', '年化收益率(%)', '最大回撤(%)', '夏普比率', '胜率(%)', '交易次数', '最终价值'];
  const csvContent = [
    headers.join(','),
    ...sortedOptimizationResults.value.map(row => [
      row.stock_code,
      row.short_period,
      row.long_period,
      row.return_rate?.toFixed(2) || 'N/A',
      row.annualized_return?.toFixed(2) || 'N/A',
      row.max_drawdown?.toFixed(2) || 'N/A',
      row.sharpe_ratio?.toFixed(3) || 'N/A',
      row.win_rate?.toFixed(2) || 'N/A',
      row.transaction_count || 0,
      row.final_value?.toFixed(2) || 'N/A'
    ].join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `参数优化回测结果_${new Date().toISOString().slice(0, 10)}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  ElMessage.success('结果已导出');
};

// 显示优化图表
const showOptimizationChart = () => {
  ElMessage.info('优化图表功能开发中...');
};

// 优化投资组合图表初始化
const initOptimizationPortfolioChart = () => {
  if (optimizationChartInstance) {
    optimizationChartInstance.dispose();
  }
  if (!optimizationPortfolioChart.value || !selectedOptimizationResult.value || !selectedOptimizationResult.value.portfolio_values) return;

  optimizationChartInstance = echarts.init(optimizationPortfolioChart.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: params => {
        const date = params[0].axisValue;
        const value = params[0].value;
        const dailyData = selectedOptimizationResult.value.daily_portfolio.find(d => d.date === date);
        let dailyInfo = '';
        if (dailyData) {
          dailyInfo = `日期: ${date}<br/>` +
                      `总资产: ${formatCurrency(value)}<br/>` +
                      `现金: ${formatCurrency(dailyData.cash)}<br/>` +
                      `股票市值: ${formatCurrency(dailyData.stock_value)}<br/>` +
                      `持仓股数: ${dailyData.position}<br/>` +
                      `当日股价: ${formatCurrency(dailyData.price)}`;
        } else {
          dailyInfo = `日期: ${date}<br/>总资产: ${formatCurrency(value)}`;
        }
        return dailyInfo;
      }
    },
    xAxis: {
      type: 'category',
      data: selectedOptimizationResult.value.daily_portfolio.map(item => item.date),
      boundaryGap: false,
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLabel: {
        formatter: function (value) {
          return formatCurrency(value, 0);
        }
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 10,
        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '80%',
        handleStyle: {
          color: '#fff',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2
        }
      }
    ],
    series: [
      {
        name: '投资组合价值',
        type: 'line',
        smooth: true,
        data: selectedOptimizationResult.value.portfolio_values,
        itemStyle: {
            color: '#5470C6'
        },
        areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(84, 112, 198, 0.3)'
            }, {
                offset: 1,
                color: 'rgba(84, 112, 198, 0)'
            }])
        }
      }
    ],
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      containLabel: true
    }
  };
  optimizationChartInstance.setOption(option);
};

// 格式化函数
const formatCurrency = (value, precision = 2) => {
  if (value === null || value === undefined) return 'N/A';
  return `¥${Number(value).toFixed(precision)}`;
};

const formatPercentage = (value, precision = 2) => {
  if (value === null || value === undefined) return 'N/A';
  return `${Number(value).toFixed(precision)}%`;
};

const cellFormatCurrency = (row, column, cellValue) => {
  return formatCurrency(cellValue);
};

const cellFormatPercentage = (row, column, cellValue) => {
  return formatPercentage(cellValue);
};

// 确保在组件卸载时销毁图表
import { onUnmounted } from 'vue';
onUnmounted(() => {
  if (optimizationChartInstance) {
    optimizationChartInstance.dispose();
  }
});

</script>

<style scoped>
.parameter-optimization-backtest-page {
  padding: 20px;
}
.page-card {
  margin-bottom: 20px;
}
.backtest-form .el-date-picker {
  width: 100%;
}
.backtest-form .el-input-number {
  width: 100%;
}
.loading-section, .error-section, .optimization-results-section, .selected-result-section {
  margin-top: 20px;
}
.section-card {
  margin-bottom: 20px;
}
.card-header {
  font-size: 18px;
  font-weight: bold;
}
.card-header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-actions {
  display: flex;
  gap: 10px;
}
.el-descriptions {
  margin-top: 10px;
}

/* 股票代码预览样式 */
.stock-codes-preview {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}
.codes-count {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

/* 参数预览样式 */
.parameter-preview {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #bfdbfe;
}
.preview-label {
  font-weight: 500;
  color: #1e40af;
}
.preview-text {
  color: #1e3a8a;
  font-size: 14px;
}

/* 盈亏颜色 */
.profit {
  color: #67c23a;
  font-weight: bold;
}
.loss {
  color: #f56c6c;
  font-weight: bold;
}

/* 表格行点击效果 */
.el-table__row {
  cursor: pointer;
}
.el-table__row:hover {
  background-color: #f5f7fa;
}

/* 筛选工具栏样式 */
.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.filter-stats {
  display: flex;
  gap: 8px;
}

.filter-stats .el-tag {
  font-size: 12px;
}
</style>
