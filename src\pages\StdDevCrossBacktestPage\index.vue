<template>
  <div class="std-dev-cross-backtest-page">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <span>标准差交叉策略回测</span>
        </div>
      </template>

      <!-- 参数输入表单 -->
      <el-form :model="form" label-width="120px" class="backtest-form">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="股票代码">
              <el-input
                v-model="form.stockCodesText"
                type="textarea"
                :rows="3"
                placeholder="请输入股票代码，多个代码用逗号、分号或换行分隔，例如: 000001.SZ,000002.SZ,600000.SH"
                @input="parseStockCodes"
              ></el-input>
              <div class="stock-codes-preview" v-if="stockCodes.length > 0">
                <el-tag
                  v-for="code in stockCodes"
                  :key="code"
                  closable
                  @close="removeStockCode(code)"
                  style="margin: 2px;"
                >
                  {{ code }}
                </el-tag>
                <div class="codes-count">共 {{ stockCodes.length }} 只股票</div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始日期">
              <el-date-picker v-model="form.startDate" type="date" placeholder="选择开始日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束日期">
              <el-date-picker v-model="form.endDate" type="date" placeholder="选择结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="初始资金">
              <el-input-number v-model="form.initialCapital" :min="1000" :step="10000" placeholder="例如: 100000"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="短周期">
              <el-input-number v-model="form.shortPeriod" :min="1" placeholder="例如: 2"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="长周期">
              <el-input-number v-model="form.longPeriod" :min="1" placeholder="例如: 40"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="每次交易股数">
              <el-input-number v-model="form.sharesPerTrade" :min="100" :step="100" placeholder="例如: 100"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="runBatchBacktest" :loading="loading">
                {{ loading ? `执行中 (${progress.current}/${progress.total})` : '执行批量回测' }}
              </el-button>
              <el-button @click="resetForm">重置参数</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 回测结果展示 -->
    <div v-if="loading" class="loading-section">
      <el-card>
        <template #header>执行进度</template>
        <div v-if="progress.total > 0">
          <el-progress
            :percentage="Math.round((progress.current / progress.total) * 100)"
            :status="progress.error ? 'exception' : 'success'"
          />
          <p style="margin-top: 10px;">
            正在处理: {{ progress.currentStock }} ({{ progress.current }}/{{ progress.total }})
          </p>
        </div>
        <el-skeleton :rows="5" animated />
      </el-card>
    </div>

    <div v-if="error" class="error-section">
      <el-alert :title="error" type="error" show-icon :closable="false"></el-alert>
    </div>

    <!-- 批量回测结果汇总 -->
    <div v-if="batchResults.length > 0 && !loading && !error" class="batch-results-section">
      <el-card class="summary-card section-card">
        <template #header>
          <div class="card-header-with-actions">
            <span>批量回测结果汇总</span>
            <div class="header-actions">
              <el-button size="small" @click="exportResults">导出结果</el-button>
              <el-button size="small" @click="showComparisonChart">对比图表</el-button>
            </div>
          </div>
        </template>

        <!-- 筛选工具栏 -->
        <div class="filter-toolbar">
          <div class="filter-section">
            <span class="filter-label">股票状态筛选：</span>
            <el-radio-group v-model="lastTradeFilter" @change="handleFilterChange" size="small">
              <el-radio-button label="all">全部 ({{ batchResults.length }})</el-radio-button>
              <el-radio-button label="last_day_buy">今日买入 ({{ lastDayBuyStocks.length }})</el-radio-button>
              <el-radio-button label="holding">持仓中 ({{ holdingStocks.length }})</el-radio-button>
              <el-radio-button label="last_day_sell">今日卖出 ({{ lastDaySellStocks.length }})</el-radio-button>
              <el-radio-button label="sold">已清仓 ({{ soldStocks.length }})</el-radio-button>
            </el-radio-group>
          </div>
          <div class="filter-stats">
            <div class="stats-row">
              <el-tag type="success" size="small">今日买入: {{ lastDayBuyStocks.length }}只</el-tag>
              <el-tag type="info" size="small">持仓中: {{ holdingStocks.length }}只</el-tag>
              <el-tag type="warning" size="small">今日卖出: {{ lastDaySellStocks.length }}只</el-tag>
              <el-tag type="danger" size="small">已清仓: {{ soldStocks.length }}只</el-tag>
            </div>
            <el-button-group size="small" style="margin-top: 8px;">
              <el-button
                size="small"
                @click="showStockCodes('last_day_buy')"
                :disabled="lastDayBuyStocks.length === 0"
              >
                今日买入代码
              </el-button>
              <el-button
                size="small"
                @click="showStockCodes('holding')"
                :disabled="holdingStocks.length === 0"
              >
                持仓代码
              </el-button>
              <el-button
                size="small"
                @click="showStockCodes('last_day_sell')"
                :disabled="lastDaySellStocks.length === 0"
              >
                今日卖出代码
              </el-button>
              <el-button
                size="small"
                @click="showStockCodes('sold')"
                :disabled="soldStocks.length === 0"
              >
                已清仓代码
              </el-button>
            </el-button-group>
          </div>
        </div>

        <el-table :data="filteredBatchResults" stripe border max-height="600px" @row-click="selectStock">
          <el-table-column prop="stock_code" label="股票代码" width="120" sortable></el-table-column>
          <el-table-column label="股票状态" width="120" sortable>
            <template #default="scope">
              <div class="status-cell">
                <el-tag
                  :type="getStatusTagType(scope.row)"
                  size="small"
                >
                  {{ getStatusText(scope.row) }}
                </el-tag>
                <div class="status-detail" v-if="getLastTradeInfo(scope.row).date">
                  {{ getLastTradeInfo(scope.row).date }}
                  <el-icon v-if="getLastTradeInfo(scope.row).isLastDay" class="last-day-icon">
                    <Clock />
                  </el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="return_rate" label="总收益率" width="120" sortable :formatter="cellFormatPercentage">
            <template #default="scope">
              <span :class="scope.row.return_rate >= 0 ? 'profit' : 'loss'">
                {{ formatPercentage(scope.row.return_rate) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="annualized_return" label="年化收益率" width="130" sortable :formatter="cellFormatPercentage">
            <template #default="scope">
              <span :class="scope.row.annualized_return >= 0 ? 'profit' : 'loss'">
                {{ formatPercentage(scope.row.annualized_return) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="max_drawdown" label="最大回撤" width="120" sortable :formatter="cellFormatPercentage"></el-table-column>
          <el-table-column prop="sharpe_ratio" label="夏普比率" width="120" sortable>
            <template #default="scope">
              {{ scope.row.sharpe_ratio ? scope.row.sharpe_ratio.toFixed(3) : 'N/A' }}
            </template>
          </el-table-column>
          <el-table-column prop="win_rate" label="胜率" width="100" sortable :formatter="cellFormatPercentage"></el-table-column>
          <el-table-column prop="transaction_count" label="交易次数" width="100" sortable></el-table-column>
          <el-table-column prop="final_value" label="最终价值" width="130" sortable :formatter="cellFormatCurrency"></el-table-column>
          <el-table-column prop="investment_efficiency" label="投资效率" width="120" sortable :formatter="cellFormatPercentage"></el-table-column>
          <el-table-column label="操作" width="160" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" @click.stop="viewDetails(scope.row)">详情</el-button>
              <el-button size="small" type="success" @click.stop="analyzeStock(scope.row)" style="margin-left: 5px;">分析</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 单个股票详细结果展示 -->
    <div v-if="selectedStockResult && !loading && !error" class="results-section">
      <el-row :gutter="20">
        <!-- 左侧：核心指标 和 交易记录 -->
        <el-col :span="16">
          <el-card class="summary-card section-card">
            <template #header>核心指标</template>
            <el-descriptions :column="3" border>
              <el-descriptions-item label="股票代码">{{ backtestResult.stock_code }}</el-descriptions-item>
              <el-descriptions-item label="回测周期">{{ backtestResult.start_date }} 至 {{ backtestResult.end_date }}</el-descriptions-item>
              <el-descriptions-item label="初始资金">{{ formatCurrency(backtestResult.initial_capital) }}</el-descriptions-item>
              
              <el-descriptions-item label="最终价值">{{ formatCurrency(backtestResult.final_value) }}</el-descriptions-item>
              <el-descriptions-item label="总收益额">{{ formatCurrency(backtestResult.total_return) }}</el-descriptions-item>
              <el-descriptions-item label="总收益率">{{ formatPercentage(backtestResult.return_rate) }}</el-descriptions-item>

              <el-descriptions-item label="年化收益率">{{ formatPercentage(backtestResult.annualized_return) }}</el-descriptions-item>
              <el-descriptions-item label="最大回撤">{{ formatPercentage(backtestResult.max_drawdown) }}</el-descriptions-item>
              <el-descriptions-item label="胜率">{{ formatPercentage(backtestResult.win_rate) }}</el-descriptions-item>

              <el-descriptions-item label="夏普比率">{{ backtestResult.sharpe_ratio ? backtestResult.sharpe_ratio.toFixed(3) : 'N/A' }}</el-descriptions-item>
              <el-descriptions-item label="最大投入资金">{{ formatCurrency(backtestResult.max_investment) }}</el-descriptions-item>
              <el-descriptions-item label="投资效率">{{ formatPercentage(backtestResult.investment_efficiency) }}</el-descriptions-item>
              
              <el-descriptions-item label="交易总次数">{{ backtestResult.transaction_count }}</el-descriptions-item>
              <el-descriptions-item label="买入次数">{{ backtestResult.buy_count }}</el-descriptions-item>
              <el-descriptions-item label="卖出次数">{{ backtestResult.sell_count }}</el-descriptions-item>

              <el-descriptions-item label="最大连买次数">{{ backtestResult.max_consecutive_buys }}</el-descriptions-item>
              <el-descriptions-item label="最大连卖次数">{{ backtestResult.max_consecutive_sells }}</el-descriptions-item>
              <el-descriptions-item label="平均持仓天数">{{ backtestResult.avg_holding_period ? backtestResult.avg_holding_period.toFixed(2) : 'N/A' }} 天</el-descriptions-item>
            </el-descriptions>
          </el-card>

          <el-card class="transactions-card section-card">
            <template #header>交易记录</template>
            <el-table :data="backtestResult.transactions" stripe border max-height="500px">
              <el-table-column prop="date" label="日期" width="110" sortable></el-table-column>
              <el-table-column prop="type" label="类型" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.type === 'buy' ? 'success' : 'danger'">{{ scope.row.type === 'buy' ? '买入' : '卖出' }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="价格" width="100" :formatter="cellFormatCurrency"></el-table-column>
              <el-table-column prop="shares" label="股数" width="100"></el-table-column>
              <el-table-column label="成本/价值" width="120">
                 <template #default="scope">{{ formatCurrency(scope.row.type === 'buy' ? scope.row.cost : scope.row.value) }}</template>
              </el-table-column>
              <el-table-column prop="profit" label="利润" width="120" :formatter="cellFormatCurrencyNullable"></el-table-column>
              <el-table-column prop="cash_after" label="交易后现金" width="150" :formatter="cellFormatCurrency"></el-table-column>
              <el-table-column prop="position_after" label="交易后持仓" width="120"></el-table-column>
              <el-table-column prop="current_portfolio_value" label="交易后总资产" width="150" :formatter="cellFormatCurrency"></el-table-column>
              <el-table-column prop="total_investment" label="累计投入" width="150" :formatter="cellFormatCurrency"></el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 右侧：投资组合价值图表 -->
        <el-col :span="8">
          <el-card class="chart-card section-card">
            <template #header>投资组合价值</template>
            <div ref="portfolioChart" style="width: 100%; height: 400px;"></div>
          </el-card>
          
          <el-card class="daily-portfolio-card section-card">
            <template #header>每日资产快照 (部分)</template>
            <el-table :data="paginatedDailyPortfolio" stripe border max-height="400px">
              <el-table-column prop="date" label="日期" sortable></el-table-column>
              <el-table-column prop="portfolio_value" label="总资产" :formatter="cellFormatCurrency"></el-table-column>
              <el-table-column prop="cash" label="现金" :formatter="cellFormatCurrency"></el-table-column>
              <el-table-column prop="stock_value" label="股票市值" :formatter="cellFormatCurrency"></el-table-column>
            </el-table>
            <el-pagination
              small
              background
              layout="prev, pager, next"
              :total="backtestResult.daily_portfolio.length"
              :page-size="dailyPortfolioPageSize"
              @current-change="handleDailyPortfolioPageChange"
              class="pagination-center"
            />
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Clock } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import axios from 'axios'; // 假设项目中已配置axios
import * as echarts from 'echarts'; // 引入ECharts

// API基础路径，请根据实际情况修改
const API_BASE_URL = 'http://localhost:8000/api/period_change_pct_stats';

// 路由实例
const router = useRouter();

// Helper function to format date as YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Get today's date and one year ago date
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(today.getFullYear() - 1);

const form = reactive({
  stockCodesText: '600320,002554,000571,000890,600243,600759,600892,002427,600467,002086,002498,002306,002523,600968,600871,000796,601106,601989,600665,600569,601015,002628,002210,601399,000573,600518,601218,600282,002224,600339,600751,601678,000955,600208,002069,000035,002110,000898,601068,000592,600169,600117,601003,600798,002694,002256,002682,601515,600575,000959,002047,600187,600581,600231,600782,600382,002408,601008,000010,601000,600017,000428,601228,601996,600433,000518,600308,000692,002638,000755,000637,601866,601518,600020,002154,002471,600180,002377,600368,000720,601880,601016,601975,600082,601700,600805,600496,601107,000972,002266,000523,000407,601588,002060,000825,601018,600500,600863,001213,002076,002397,603797,000900,600572,600103,000709,601868,600726,000912,000897,000088,002641,000078,002495,600252,600170,000927,000767,600939,601991,600179,600815,002132,002589,000820,600008,002356,601618,601326,002081,601188,600681,600928,600794,000778,600033,002375,001896,600649,600035,600507,002135,600468,600717,002107,002062,600744,002157,000572,000659,000949,601005,000717,600490,601368,600578,000883,600683,603843,002958,600248,600502,000965,002565,600526,600935,000059,000552,002061,002206,002663,600491,600512,002573,600684,601718,000926,601158,000966,600617,002613,600395,600333,000591,002386,600802,000514,000690,002307,002839,000090,601568,600853,002191,000790,600115,003816,002108,002303,002109,000558,601333,600293,600448,000797,601916,000631,600108,600477,600844,600675,002133,600545,002506,002445,000069,600168,000539,001227,000718,600149,002562,600795,002672,600905,000055,002807,002948,002385,000517,000652,002620,002482,000863,603077,600010,600594,600708,000632,600881,600790,600261,002666,002578,600688,601992,002567,601968,603336,000520,601669,000812,601010,002687,600307,000751,600221,600488,601778,003035,600075,600022,601860,600396,002004,600067,600740,002793,002437,600724,603616,600664,601366,000056,000068,600936,002501,000698,600533,002671,600959,000563,600725,600310,002969,600076,002330,002145,600527,600658,002702,002431,600808,600800,002084,002269,600121,000639,000677,600543,000826,000060,002936,600626,002548,002390,600219,600963,002012,601599,002426,002476,002688,000031,002775,600162,000620,600052,600370,002218,002263,000850,000838,002344,002370,600981,600567,600748,603778,000782,002285,600159,002413,600016,002160,600966,002678,002102,002228,600635,000545,603818,002305,002146,002496,600157,000839,600227,000402,603601,002127,600691,600429,002486,601212,000564,000725,002524,600226,600604,601086,600266,600540,600300,002717,002607,002798,600828,600094,601099,002247,601929,600207,600255,600239,600651,601818,600369,000892,600515,600643,002021,601113,600854,000931,600537,002470,600606,600166,600322,600400,002188,002314,002662,600982,001330,600791,000599,000813,600816,000629,600193,000630,002551,002631,600792,000627,000008,000415,000802,000981,002374,601162,601908,600653,600824,600984,002219,603669,000607,002513,600383,603188,000750,002494,600110,002652,002489,600770,000546,000509,600654,600657,600880,000727,002321,603021,002177,600340,002716,603980,603183,000560,600337,002542,600743,601375,600503,600425,600172,600376,002323,000882,000510,002121,002659,601388,600130,002131,600622,601279,002418,000420,603030,000100,002622,600439,600423,600361,002211,600807,600676,600280,002535,002596,601011,000816,002239,600355,002522,601828,002640,002634,000036,002421,600868,000691,002172,000723,600281,002067,000761,002183,600758,002630,601616,603335,600386,000785,600666,000980,000903,002348,002526,600525,600595,000608,601933,601619,600960,600408,600063,600495,002689,002329,600716,600705,002122,002072,000757,002162,002570,600403',
  startDate: formatDate(oneYearAgo), // 默认开始日期为一年前的今天
  endDate: formatDate(today),       // 默认结束日期为今天
  shortPeriod: 2,                 // 默认短周期为2
  longPeriod: 30,                  // 默认长周期为30
  initialCapital: 100000,
  sharesPerTrade: 100,
});

const loading = ref(false);
const error = ref(null);
const backtestResult = ref(null);
const batchResults = ref([]);
const selectedStockResult = ref(null);
const stockCodes = ref([]);
const portfolioChart = ref(null); // ECharts实例的DOM引用
let chartInstance = null; // ECharts实例

// 筛选相关状态
const lastTradeFilter = ref('all'); // 'all', 'buy', 'sell'

// 进度状态
const progress = reactive({
  current: 0,
  total: 0,
  currentStock: '',
  error: false
});

// 解析股票代码
const parseStockCodes = () => {
  const text = form.stockCodesText.trim();
  if (!text) {
    stockCodes.value = [];
    return;
  }

  // 支持逗号、分号、换行分隔
  const codes = text
    .split(/[,;，；\n\r]+/)
    .map(code => code.trim().toUpperCase())
    .filter(code => code.length > 0);

  stockCodes.value = [...new Set(codes)]; // 去重
};

// 移除股票代码
const removeStockCode = (codeToRemove) => {
  stockCodes.value = stockCodes.value.filter(code => code !== codeToRemove);
  form.stockCodesText = stockCodes.value.join(',');
};

// 初始化解析股票代码
parseStockCodes();

const resetForm = () => {
  form.stockCodesText = '000001';
  form.startDate = formatDate(oneYearAgo);
  form.endDate = formatDate(today);
  form.shortPeriod = 2;                 // 重置短周期为2
  form.longPeriod = 30;                  // 重置长周期为30
  form.initialCapital = 100000;
  form.sharesPerTrade = 100;
  backtestResult.value = null;
  batchResults.value = [];
  selectedStockResult.value = null;
  stockCodes.value = [];
  error.value = null;
  parseStockCodes();
};

// 批量回测函数
const runBatchBacktest = async () => {
  if (!form.startDate || !form.endDate || !form.shortPeriod || !form.longPeriod || !form.initialCapital || !form.sharesPerTrade) {
    ElMessage.error('所有参数均为必填项！');
    return;
  }
  if (form.shortPeriod >= form.longPeriod) {
    ElMessage.error('短周期必须小于长周期！');
    return;
  }
  if (stockCodes.value.length === 0) {
    ElMessage.error('请至少输入一个股票代码！');
    return;
  }

  loading.value = true;
  error.value = null;
  batchResults.value = [];
  selectedStockResult.value = null;

  // 初始化进度
  progress.current = 0;
  progress.total = stockCodes.value.length;
  progress.error = false;

  try {
    const results = [];

    for (let i = 0; i < stockCodes.value.length; i++) {
      const stockCode = stockCodes.value[i];
      progress.current = i + 1;
      progress.currentStock = stockCode;

      try {
        const params = {
          start_date: form.startDate,
          end_date: form.endDate,
          short_period: form.shortPeriod,
          long_period: form.longPeriod,
          initial_capital: form.initialCapital,
          shares_per_trade: form.sharesPerTrade,
        };

        const response = await axios.get(`${API_BASE_URL}/backtest/analyze/${stockCode}`, { params });
        if (response.data) {
          results.push(response.data);
        }
      } catch (err) {
        console.error(`股票 ${stockCode} 回测失败:`, err);
        // 继续处理下一个股票，不中断整个流程
        ElMessage.warning(`股票 ${stockCode} 回测失败: ${err.response?.data?.error || err.message}`);
      }
    }

    batchResults.value = results;

    if (results.length === 0) {
      ElMessage.warning('所有股票回测都失败了，请检查股票代码和参数');
    } else {
      // 统计详细状态
      const lastDayBuyCount = results.filter(stock => getLastTradeInfo(stock).status === 'last_day_buy').length;
      const holdingCount = results.filter(stock => getLastTradeInfo(stock).status === 'holding').length;
      const lastDaySellCount = results.filter(stock => getLastTradeInfo(stock).status === 'last_day_sell').length;
      const soldCount = results.filter(stock => getLastTradeInfo(stock).status === 'sold').length;

      ElMessage.success({
        message: `批量回测完成！成功处理 ${results.length}/${stockCodes.value.length} 只股票\n` +
                 `今日买入: ${lastDayBuyCount}只，持仓中: ${holdingCount}只\n` +
                 `今日卖出: ${lastDaySellCount}只，已清仓: ${soldCount}只`,
        duration: 6000
      });
    }
  } catch (err) {
    console.error("批量回测失败:", err);
    error.value = `批量回测失败: ${err.message || '未知错误'}`;
    ElMessage.error(error.value);
    progress.error = true;
  } finally {
    loading.value = false;
  }
};

// 单个股票回测函数（保留用于详情查看）
const runSingleBacktest = async (stockCode) => {
  const params = {
    start_date: form.startDate,
    end_date: form.endDate,
    short_period: form.shortPeriod,
    long_period: form.longPeriod,
    initial_capital: form.initialCapital,
    shares_per_trade: form.sharesPerTrade,
  };

  const response = await axios.get(`${API_BASE_URL}/backtest/analyze/${stockCode}`, { params });
  return response.data;
};

// 图表初始化
const initPortfolioChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  if (!portfolioChart.value || !backtestResult.value || !backtestResult.value.portfolio_values) return;

  chartInstance = echarts.init(portfolioChart.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: params => {
        const date = params[0].axisValue;
        const value = params[0].value;
        const dailyData = backtestResult.value.daily_portfolio.find(d => d.date === date);
        let dailyInfo = '';
        if (dailyData) {
          dailyInfo = `日期: ${date}<br/>` +
                      `总资产: ${formatCurrency(value)}<br/>` +
                      `现金: ${formatCurrency(dailyData.cash)}<br/>` +
                      `股票市值: ${formatCurrency(dailyData.stock_value)}<br/>` +
                      `持仓股数: ${dailyData.position}<br/>` +
                      `当日股价: ${formatCurrency(dailyData.price)}`;
        } else {
          dailyInfo = `日期: ${date}<br/>总资产: ${formatCurrency(value)}`;
        }
        return dailyInfo;
      }
    },
    xAxis: {
      type: 'category',
      data: backtestResult.value.daily_portfolio.map(item => item.date), // 使用daily_portfolio的日期作为X轴
      boundaryGap: false,
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLabel: {
        formatter: function (value) {
          return formatCurrency(value, 0); // 格式化Y轴标签
        }
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 10,
        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '80%',
        handleStyle: {
          color: '#fff',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2
        }
      }
    ],
    series: [
      {
        name: '投资组合价值',
        type: 'line',
        smooth: true,
        data: backtestResult.value.portfolio_values,
        itemStyle: {
            color: '#5470C6'
        },
        areaStyle: { // 添加面积图效果
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(84, 112, 198, 0.3)'
            }, {
                offset: 1,
                color: 'rgba(84, 112, 198, 0)'
            }])
        }
      }
    ],
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%', // 为dataZoom留出空间
      containLabel: true
    }
  };
  chartInstance.setOption(option);
};

// 格式化函数
const formatCurrency = (value, precision = 2) => {
  if (value === null || value === undefined) return 'N/A';
  return `¥${Number(value).toFixed(precision)}`;
};

const formatPercentage = (value, precision = 2) => {
  if (value === null || value === undefined) return 'N/A';
  return `${Number(value).toFixed(precision)}%`;
};

const cellFormatCurrency = (row, column, cellValue) => {
  return formatCurrency(cellValue);
};
const cellFormatCurrencyNullable = (row, column, cellValue) => {
  if (cellValue === null || cellValue === undefined) return '-';
  return formatCurrency(cellValue);
};
const cellFormatPercentage = (row, column, cellValue) => {
  return formatPercentage(cellValue);
};

// 获取股票最后交易类型和详细信息
const getLastTradeInfo = (stockResult) => {
  if (!stockResult.transactions || stockResult.transactions.length === 0) {
    return {
      type: 'unknown',
      date: null,
      isLastDay: false,
      status: 'no_trades'
    };
  }

  const lastTransaction = stockResult.transactions[stockResult.transactions.length - 1];
  const lastTradeDate = lastTransaction.date;
  const endDate = stockResult.end_date;

  // 获取当前日期（今天）
  const todayDate = formatDate(new Date());

  // 判断最后交易是否在今天（而不是回测结束日期）
  const isLastDay = lastTradeDate === todayDate;

  // 确定股票状态
  let status;
  if (lastTransaction.type === 'buy') {
    status = isLastDay ? 'last_day_buy' : 'holding'; // 今天买入 或 持仓中
  } else {
    status = isLastDay ? 'last_day_sell' : 'sold'; // 今天卖出 或 已卖出
  }

  return {
    type: lastTransaction.type,
    date: lastTradeDate,
    isLastDay: isLastDay,
    status: status,
    endDate: endDate,
    todayDate: todayDate // 添加今天日期用于调试
  };
};

// 兼容性函数：保持原有接口
const getLastTradeType = (stockResult) => {
  return getLastTradeInfo(stockResult).type;
};

// 状态显示辅助函数
const getStatusText = (stockResult) => {
  const info = getLastTradeInfo(stockResult);
  switch (info.status) {
    case 'last_day_buy':
      return '今日买入';
    case 'holding':
      return '持仓中';
    case 'last_day_sell':
      return '今日卖出';
    case 'sold':
      return '已清仓';
    case 'no_trades':
      return '无交易';
    default:
      return '未知';
  }
};

const getStatusTagType = (stockResult) => {
  const info = getLastTradeInfo(stockResult);
  switch (info.status) {
    case 'last_day_buy':
      return 'success';
    case 'holding':
      return 'info';
    case 'last_day_sell':
      return 'warning';
    case 'sold':
      return 'danger';
    case 'no_trades':
      return '';
    default:
      return '';
  }
};

// 计算属性：按详细状态分类的股票
const lastDayBuyStocks = computed(() => {
  return batchResults.value.filter(stock => getLastTradeInfo(stock).status === 'last_day_buy');
});

const holdingStocks = computed(() => {
  return batchResults.value.filter(stock => getLastTradeInfo(stock).status === 'holding');
});

const lastDaySellStocks = computed(() => {
  return batchResults.value.filter(stock => getLastTradeInfo(stock).status === 'last_day_sell');
});

const soldStocks = computed(() => {
  return batchResults.value.filter(stock => getLastTradeInfo(stock).status === 'sold');
});

// 兼容性计算属性：保持原有接口
const lastBuyStocks = computed(() => {
  return batchResults.value.filter(stock => getLastTradeType(stock) === 'buy');
});

const lastSellStocks = computed(() => {
  return batchResults.value.filter(stock => getLastTradeType(stock) === 'sell');
});

// 计算属性：筛选后的结果
const filteredBatchResults = computed(() => {
  switch (lastTradeFilter.value) {
    case 'all':
      return batchResults.value;
    case 'last_day_buy':
      return lastDayBuyStocks.value;
    case 'holding':
      return holdingStocks.value;
    case 'last_day_sell':
      return lastDaySellStocks.value;
    case 'sold':
      return soldStocks.value;
    // 兼容旧的筛选条件
    case 'buy':
      return lastBuyStocks.value;
    case 'sell':
      return lastSellStocks.value;
    default:
      return batchResults.value;
  }
});

// 筛选变化处理
const handleFilterChange = (value) => {
  console.log('筛选条件变更:', value);
  // 可以在这里添加额外的处理逻辑
};

// 表格操作函数
const selectStock = (row) => {
  selectedStockResult.value = row;
  backtestResult.value = row;
  // 初始化图表
  nextTick(() => {
    if (row.portfolio_values && row.portfolio_values.length > 0) {
      initPortfolioChart();
    }
  });
};

const viewDetails = (row) => {
  selectStock(row);
  // 滚动到详情区域
  nextTick(() => {
    const detailsElement = document.querySelector('.results-section');
    if (detailsElement) {
      detailsElement.scrollIntoView({ behavior: 'smooth' });
    }
  });
};

// 分析股票 - 在新标签页打开批量期间涨跌统计页面
const analyzeStock = (row) => {
  const stockCode = row.stock_code;

  // 构建目标URL，包含股票代码和自动分析标记
  const targetUrl = router.resolve({
    name: 'BatchPeriodChangeStats',
    query: {
      stockCode: stockCode,
      autoAnalyze: 'true' // 标记需要自动执行分析
    }
  });

  // 在新标签页中打开
  window.open(targetUrl.href, '_blank');

  ElMessage.success(`正在新标签页中打开分析页面，股票代码: ${stockCode}`);
};

// 导出结果
const exportResults = () => {
  if (batchResults.value.length === 0) {
    ElMessage.warning('没有可导出的数据');
    return;
  }

  // 简单的CSV导出
  const headers = ['股票代码', '最后交易', '总收益率(%)', '年化收益率(%)', '最大回撤(%)', '夏普比率', '胜率(%)', '交易次数', '最终价值', '投资效率(%)'];
  const dataToExport = lastTradeFilter.value === 'all' ? batchResults.value : filteredBatchResults.value;
  const csvContent = [
    headers.join(','),
    ...dataToExport.map(row => [
      row.stock_code,
      getLastTradeType(row) === 'buy' ? '买入' : '卖出',
      row.return_rate?.toFixed(2) || 'N/A',
      row.annualized_return?.toFixed(2) || 'N/A',
      row.max_drawdown?.toFixed(2) || 'N/A',
      row.sharpe_ratio?.toFixed(3) || 'N/A',
      row.win_rate?.toFixed(2) || 'N/A',
      row.transaction_count || 0,
      row.final_value?.toFixed(2) || 'N/A',
      row.investment_efficiency?.toFixed(2) || 'N/A'
    ].join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `标准差交叉策略回测结果_${new Date().toISOString().slice(0, 10)}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  ElMessage.success('结果已导出');
};

// 显示对比图表
const showComparisonChart = () => {
  ElMessage.info('对比图表功能开发中...');
};

// 显示股票代码列表
const showStockCodes = (type) => {
  let stocks, typeText;

  switch (type) {
    case 'last_day_buy':
      stocks = lastDayBuyStocks.value;
      typeText = '今日买入';
      break;
    case 'holding':
      stocks = holdingStocks.value;
      typeText = '持仓中';
      break;
    case 'last_day_sell':
      stocks = lastDaySellStocks.value;
      typeText = '今日卖出';
      break;
    case 'sold':
      stocks = soldStocks.value;
      typeText = '已清仓';
      break;
    // 兼容旧的类型
    case 'buy':
      stocks = lastBuyStocks.value;
      typeText = '最后买入';
      break;
    case 'sell':
      stocks = lastSellStocks.value;
      typeText = '最后卖出';
      break;
    default:
      stocks = [];
      typeText = '未知';
  }

  const codes = stocks.map(stock => stock.stock_code).join(', ');

  // 构建详细信息
  let detailInfo = codes || '暂无数据';
  if (stocks.length > 0) {
    detailInfo += '\n\n详细信息:\n';
    detailInfo += stocks.map(stock => {
      const info = getLastTradeInfo(stock);
      return `${stock.stock_code}: ${info.date} (${info.isLastDay ? '今日' : '历史'})`;
    }).join('\n');
  }

  ElMessageBox.alert(
    detailInfo,
    `${typeText}股票代码 (${stocks.length}只)`,
    {
      confirmButtonText: '复制代码',
      callback: () => {
        if (codes) {
          navigator.clipboard.writeText(codes).then(() => {
            ElMessage.success('股票代码已复制到剪贴板');
          }).catch(() => {
            ElMessage.warning('复制失败，请手动复制');
          });
        }
      }
    }
  );
};

// 每日资产快照分页
const dailyPortfolioPage = ref(1);
const dailyPortfolioPageSize = ref(10);

const paginatedDailyPortfolio = computed(() => {
  if (!backtestResult.value || !backtestResult.value.daily_portfolio) {
    return [];
  }
  const start = (dailyPortfolioPage.value - 1) * dailyPortfolioPageSize.value;
  const end = start + dailyPortfolioPageSize.value;
  return backtestResult.value.daily_portfolio.slice(start, end);
});

const handleDailyPortfolioPageChange = (page) => {
  dailyPortfolioPage.value = page;
};

// 确保在组件卸载时销毁图表
import { onUnmounted } from 'vue';
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
});

</script>

<style scoped>
.std-dev-cross-backtest-page {
  padding: 20px;
}
.page-card {
  margin-bottom: 20px;
}
.backtest-form .el-date-picker {
  width: 100%;
}
.backtest-form .el-input-number {
  width: 100%;
}
.loading-section, .error-section, .batch-results-section {
  margin-top: 20px;
}
.results-section {
  margin-top: 20px;
}
.section-card {
  margin-bottom: 20px;
}
.card-header {
  font-size: 18px;
  font-weight: bold;
}
.card-header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-actions {
  display: flex;
  gap: 10px;
}
.el-descriptions {
  margin-top: 10px;
}
.pagination-center {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

/* 股票代码预览样式 */
.stock-codes-preview {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}
.codes-count {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

/* 盈亏颜色 */
.profit {
  color: #67c23a;
  font-weight: bold;
}
.loss {
  color: #f56c6c;
  font-weight: bold;
}

/* 表格行点击效果 */
.el-table__row {
  cursor: pointer;
}
.el-table__row:hover {
  background-color: #f5f7fa;
}

/* 筛选工具栏样式 */
.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.filter-stats {
  display: flex;
  gap: 8px;
}

.filter-stats .el-tag {
  font-size: 12px;
}

.stats-row {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 状态单元格样式 */
.status-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.status-detail {
  font-size: 11px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 2px;
}

.last-day-icon {
  color: #e6a23c;
  font-size: 12px;
}
</style>