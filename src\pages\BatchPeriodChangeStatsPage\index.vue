<template>
  <el-card class="page-container">
    <template #header>
      <div class="card-header">
        <span>批量周期性涨跌幅统计</span>
      </div>
    </template>

    <!-- 查询表单 -->
    <el-form :model="form" :rules="rules" ref="queryForm" label-width="120px" class="query-form">
      <el-form-item label="股票代码" prop="stockCode">
        <el-input v-model="form.stockCode" placeholder="例如：000001" style="width: 220px;"></el-input>
      </el-form-item>
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker
          v-model="form.startDate"
          type="date"
          placeholder="选择开始日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker
          v-model="form.endDate"
          type="date"
          placeholder="选择结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="最小周期(天)" prop="minPeriod">
        <el-input-number v-model="form.minPeriod" :min="1" placeholder="例如：2"></el-input-number>
      </el-form-item>
      <el-form-item label="最大周期(天)" prop="maxPeriod">
        <el-input-number v-model="form.maxPeriod" :min="1" placeholder="例如：40"></el-input-number>
      </el-form-item>
      <el-form-item label="周期步长" prop="periodStep">
        <el-input-number v-model="form.periodStep" :min="1" placeholder="例如：2"></el-input-number>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleQuery" :loading="loading">批量分析</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 结果显示区域 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-if="!loading && queryPerformed && !errorMsg" class="results-container">
      <el-alert
        :title="`批量分析结果 - ${form.stockCode} (${form.startDate} 至 ${form.endDate})`"
        type="success"
        :closable="false"
        show-icon
        style="margin-bottom: 20px;"
      >
        <p>分析周期范围: {{ form.minPeriod }}天 至 {{ form.maxPeriod }}天，步长: {{ form.periodStep }}天</p>
        <p>总交易天数: {{ totalTradingDays }} | 分析周期总数: {{ periodResults.length }}</p>
      </el-alert>

      <!-- 周期选择器 -->
      <div class="period-selector" style="margin-bottom: 20px;">
        <span>选择查看周期: </span>
        <el-radio-group v-model="selectedPeriodIndex" @change="handlePeriodChange">
          <el-radio-button v-for="(result, index) in periodResults" :key="index" :label="index">
            {{ result.period }}天
          </el-radio-button>
        </el-radio-group>
      </div>

      <!-- 当前选中周期的详细结果 -->
      <div v-if="currentPeriodResult && currentPeriodResult.stats && currentPeriodResult.stats.length > 0">
        <h3>{{ currentPeriodResult.period }}天周期详细统计</h3>
        
        <el-table :data="currentPeriodResult.stats" border stripe height="400" style="width: 100%">
          <el-table-column prop="group_start_date" label="周期开始日期" width="150" sortable></el-table-column>
          <el-table-column prop="group_end_date" label="周期结束日期" width="150" sortable></el-table-column>
          <el-table-column label="周期内涨跌幅列表" min-width="250">
              <template #default="scope">
                  <el-tag
                      v-for="(pct, index) in scope.row.change_pct_list"
                      :key="index"
                      :type="pct >= 0 ? 'success' : 'danger'"
                      effect="light"
                      size="small"
                      style="margin-right: 5px; margin-bottom: 5px;"
                  >
                      {{ pct.toFixed(2) }}%
                  </el-tag>
              </template>
          </el-table-column>
          <el-table-column prop="mean" label="均值(%)" width="120" sortable>
              <template #default="scope">
                  <span :class="{'positive-text': scope.row.mean >= 0, 'negative-text': scope.row.mean < 0}">
                      {{ scope.row.mean.toFixed(2) }}%
                  </span>
              </template>
          </el-table-column>
          <el-table-column prop="variance" label="方差" width="120" sortable>
               <template #default="scope">
                  {{ scope.row.variance.toFixed(4) }}
              </template>
          </el-table-column>
          <el-table-column label="标准差" width="120" sortable>
              <template #default="scope">
                  {{ scope.row.std_dev?.toFixed(4) }}
              </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 各周期均值对比图表 -->
      <div v-if="periodResults.length > 1" class="period-comparison-chart" style="margin-top: 20px;">
        <h3>各周期均值对比</h3>
        <div ref="periodComparisonChartRef" style="width: 100%; height: 400px;"></div>
      </div>

      <!-- 多周期数据对比图表 -->
      <div v-if="periodResults.length > 0" class="multi-period-chart" style="margin-top: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <h3>多周期涨跌幅数据对比</h3>
          <div>
            <el-checkbox v-model="showStdDev" style="margin-right: 15px;">显示标准差范围</el-checkbox>
            <el-checkbox-group v-model="selectedPeriodsForChart">
              <el-checkbox 
                v-for="(result, index) in periodResults" 
                :key="index" 
                :label="index"
              >
                {{ result.period }}天周期
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div ref="multiPeriodChartRef" style="width: 100%; height: 500px;"></div>
      </div>
    </div>

    <el-empty
        v-if="!loading && queryPerformed && !errorMsg && (!periodResults || periodResults.length === 0)"
        description="未查询到相关统计数据，请调整查询条件或确认该时段有足够的交易数据。"
    ></el-empty>

    <el-alert
        v-if="errorMsg"
        :title="'查询出错'"
        type="error"
        :description="errorMsg"
        show-icon
        @close="errorMsg = ''"
        style="margin-top: 20px;"
      ></el-alert>

    <!-- K线图组件 -->
    <div v-if="showKlineChart" class="kline-chart-container">
      <KLineMAChart
        ref="klineChartRef"
        :dates="klineChartData.dates"
        :klineData="klineChartData.klineData"
        :maData="klineChartData.maData"
        :title="klineChartData.title"
        :periods="klineChartData.periods">
      </KLineMAChart>
    </div>

    <!-- 当前周期统计图表 -->
    <div v-if="showKlineChart && currentPeriodResult && currentPeriodResult.stats && currentPeriodResult.stats.length > 0" class="stats-chart-container" style="margin-top: 20px;">
      <div ref="statsChartRef" style="width: 100%; height: 550px;"></div>
    </div>

  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted, watch, nextTick, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useRoute } from 'vue-router';
import axios from 'axios';
import * as echarts from 'echarts';
import KLineMAChart from '@/pages/MAAnalysisPage/components/KLineMAChart.vue';
import { getMAPositionAnalysis, formatMAAnalysisData } from '@/api/maAnalysis';

const queryForm = ref(null);
const klineChartRef = ref(null);
const statsChartRef = ref(null);
const periodComparisonChartRef = ref(null);
const multiPeriodChartRef = ref(null);

// 路由实例
const route = useRoute();

let statsChartInstance = null;
let periodComparisonChartInstance = null;
let multiPeriodChartInstance = null;

const loading = ref(false);
const queryPerformed = ref(false);
const errorMsg = ref('');

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 计算默认日期：今天和一年前
const today = new Date();
const oneYearAgo = new Date();
oneYearAgo.setFullYear(today.getFullYear() - 1);

const defaultStartDate = formatDate(oneYearAgo);
const defaultEndDate = formatDate(today);

const form = reactive({
  stockCode: '002370', // 默认股票代码
  startDate: defaultStartDate,
  endDate: defaultEndDate,
  minPeriod: 2,  // 最小周期天数
  maxPeriod: 40, // 最大周期天数
  periodStep: 2, // 周期步长
  // 均线显示相关参数
  ma_min_period: 5,
  ma_max_period: 60,
  ma_step: 5,
  klineMaDisplayPeriods: [5, 10, 20, 30]
});

const rules = reactive({
  stockCode: [{ required: true, message: '请输入股票代码', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
  minPeriod: [{ required: true, message: '请输入最小周期', trigger: 'blur' }, {type: 'number', min:1, message: '周期必须大于0', trigger: 'blur'}],
  maxPeriod: [{ required: true, message: '请输入最大周期', trigger: 'blur' }, {type: 'number', min:1, message: '周期必须大于0', trigger: 'blur'}],
  periodStep: [{ required: true, message: '请输入周期步长', trigger: 'blur' }, {type: 'number', min:1, message: '步长必须大于0', trigger: 'blur'}],
});

// 存储所有周期的结果
const periodResults = ref([]);
// 当前选中的周期索引
const selectedPeriodIndex = ref(0);
// 总交易天数（从第一次API调用获取）
const totalTradingDays = ref(0);

// 计算当前选中的周期结果
const currentPeriodResult = computed(() => {
  if (periodResults.value.length === 0 || selectedPeriodIndex.value >= periodResults.value.length) {
    return null;
  }
  return periodResults.value[selectedPeriodIndex.value];
});

// K线图相关数据
const klineChartData = ref({
  dates: [],
  klineData: [],
  maData: {},
  title: '',
  periods: []
});

const showKlineChart = ref(false);

const selectedPeriodsForChart = ref([]);
const showStdDev = ref(true);

onMounted(() => {
  // 检查路由参数，如果有股票代码参数，自动填入并执行分析
  const stockCodeFromRoute = route.query.stockCode;
  const minPeriodFromRoute = route.query.minPeriod;
  const maxPeriodFromRoute = route.query.maxPeriod;
  const periodStepFromRoute = route.query.periodStep;
  const autoAnalyze = route.query.autoAnalyze === 'true';

  if (stockCodeFromRoute) {
    form.stockCode = stockCodeFromRoute;

    // 如果有周期参数，也自动填入
    if (minPeriodFromRoute && !isNaN(Number(minPeriodFromRoute))) {
      form.minPeriod = Number(minPeriodFromRoute);
    }
    if (maxPeriodFromRoute && !isNaN(Number(maxPeriodFromRoute))) {
      form.maxPeriod = Number(maxPeriodFromRoute);
    }
    if (periodStepFromRoute && !isNaN(Number(periodStepFromRoute))) {
      form.periodStep = Number(periodStepFromRoute);
    }

    let message = `已自动填入股票代码: ${stockCodeFromRoute}`;
    if (minPeriodFromRoute && maxPeriodFromRoute) {
      message += `，周期范围: ${minPeriodFromRoute}-${maxPeriodFromRoute}`;
      if (periodStepFromRoute) {
        message += `，步长: ${periodStepFromRoute}`;
      }
    }
    ElMessage.info(message);

    // 如果标记了自动分析，延迟执行查询
    if (autoAnalyze) {
      nextTick(() => {
        setTimeout(() => {
          handleQuery();
        }, 500); // 延迟500ms执行，确保组件完全加载
      });
    }
  }
});

// 监听K线图数据和当前周期结果的变化
watch([showKlineChart, currentPeriodResult], ([newShowKlineChart, newCurrentPeriodResult]) => {
  if (newShowKlineChart && newCurrentPeriodResult && newCurrentPeriodResult.stats && newCurrentPeriodResult.stats.length > 0) {
    nextTick(() => {
      initOrUpdateStatsChart();
    });
  }
});

// 监听周期结果数组的变化，更新周期对比图表
watch(periodResults, (newResults) => {
  if (newResults.length > 1) {
    nextTick(() => {
      initOrUpdatePeriodComparisonChart();
    });
  }
});

// 监听选中周期变化，更新多周期对比图表
watch([selectedPeriodsForChart, showStdDev], () => {
  nextTick(() => {
    initOrUpdateMultiPeriodChart();
  });
});

// 监听周期结果数组变化
watch(periodResults, (newResults, oldResults) => {
  if (newResults.length > 0) {
    // 只在首次获取数据时设置默认选中，避免覆盖用户的选择
    if (!oldResults || oldResults.length === 0) {
      selectedPeriodsForChart.value = [0]; // 默认选中第一个周期
    }
    nextTick(() => {
      initOrUpdateMultiPeriodChart();
    });
  } else {
    // 如果没有结果，清空选中的周期
    selectedPeriodsForChart.value = [];
  }
});

// 处理周期变更
const handlePeriodChange = (newIndex) => {
  // 周期变更时更新统计图表
  if (statsChartInstance) {
    statsChartInstance.dispose();
    statsChartInstance = null;
  }
  nextTick(() => {
    initOrUpdateStatsChart();
  });
};

// 处理查询
const handleQuery = async () => {
  if (!queryForm.value) {
    ElMessage.error('表单引用丢失，请刷新页面重试。');
    return;
  }

  queryForm.value.validate(async (valid) => {
    if (valid) {
      // 验证最小周期不大于最大周期
      if (form.minPeriod > form.maxPeriod) {
        ElMessage.error('最小周期不能大于最大周期！');
        return false;
      }

      loading.value = true;
      queryPerformed.value = true;
      errorMsg.value = '';

      // 重置图表实例
      if (statsChartInstance) {
        statsChartInstance.dispose();
        statsChartInstance = null;
      }
      if (periodComparisonChartInstance) {
        periodComparisonChartInstance.dispose();
        periodComparisonChartInstance = null;
      }
      if (multiPeriodChartInstance) {
        multiPeriodChartInstance.dispose();
        multiPeriodChartInstance = null;
      }

      // 重置结果数据
      periodResults.value = [];
      selectedPeriodIndex.value = 0;
      totalTradingDays.value = 0;

      // 重置K线图数据
      klineChartData.value = {
        dates: [],
        klineData: [],
        maData: {},
        title: '',
        periods: []
      };

      showKlineChart.value = false;

      try {
        // 1. 首先获取K线数据，因为这只需要调用一次
        const maApiParams = {
          stock_code: form.stockCode,
          start_date: form.startDate,
          end_date: form.endDate,
          min_period: form.ma_min_period,
          max_period: form.ma_max_period,
          step: form.ma_step
        };

        const maRawData = await getMAPositionAnalysis(maApiParams);
        
        if (maRawData) {
          const formattedChartData = formatMAAnalysisData(maRawData);
          
          klineChartData.value.dates = formattedChartData.dates;
          klineChartData.value.klineData = formattedChartData.klineData;
          klineChartData.value.maData = formattedChartData.maData;
          klineChartData.value.periods = form.klineMaDisplayPeriods;
          klineChartData.value.title = `${form.stockCode} (${form.startDate} 至 ${form.endDate}) K线图`;
          
          if (klineChartData.value.dates.length > 0 && klineChartData.value.klineData.length > 0) {
            showKlineChart.value = true;
          }
        } else {
          ElMessage.warning('K线图数据获取失败或无数据。');
        }

        // 2. 根据周期范围和步长，循环获取不同周期的统计数据
        for (let period = form.minPeriod; period <= form.maxPeriod; period += form.periodStep) {
          try {
            const statsResponse = await axios.get('/api/v1/period_change_pct_stats/calculate', {
              params: {
                stock_code: form.stockCode,
                start_date: form.startDate,
                end_date: form.endDate,
                period: period,
              },
              headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Expires': '0'
              }
            });

            if (statsResponse && statsResponse.data) {
              // 只记录第一个周期的总交易天数（因为所有周期都是相同的时间范围）
              if (periodResults.value.length === 0) {
                totalTradingDays.value = statsResponse.data.total_trading_days_in_range;
              }
              
              // 将结果添加到周期结果数组
              periodResults.value.push(statsResponse.data);
            }
          } catch (error) {
            console.error(`周期${period}天的数据获取失败:`, error);
            // 继续获取下一个周期的数据，而不是完全中断
          }
        }

        // 检查是否所有周期都获取失败
        if (periodResults.value.length === 0) {
          errorMsg.value = '所有周期的数据获取均失败，请检查查询参数。';
          ElMessage.error(errorMsg.value);
        } else {
          // 默认选择第一个周期
          selectedPeriodIndex.value = 0;
          
          // 更新统计图表
          nextTick(() => {
            if (showKlineChart.value && currentPeriodResult.value && currentPeriodResult.value.stats && currentPeriodResult.value.stats.length > 0) {
              initOrUpdateStatsChart();
            }

            // 如果有多个周期结果，初始化周期对比图表
            if (periodResults.value.length > 1) {
              initOrUpdatePeriodComparisonChart();
            }

            // 初始化多周期数据对比图表
            if (periodResults.value.length > 0 && selectedPeriodsForChart.value.length > 0) {
              initOrUpdateMultiPeriodChart();
            }
          });

          ElMessage.success(`成功获取 ${periodResults.value.length} 个周期的统计数据`);
        }
        
      } catch (error) {
        console.error("API请求错误:", error);
        if (error.response && error.response.data && error.response.data.detail) {
          errorMsg.value = `请求失败: ${error.response.data.detail}`;
        } else {
          errorMsg.value = '查询失败，请检查网络或联系管理员。';
        }
        ElMessage.error(errorMsg.value);
      } finally {
        loading.value = false;
      }
    } else {
      ElMessage.error('请检查表单输入项!');
      return false;
    }
  });
};

// 重置表单
const resetForm = () => {
  if (queryForm.value) {
    queryForm.value.resetFields();
  }
  
  // 重置结果和图表
  periodResults.value = [];
  selectedPeriodIndex.value = 0;
  totalTradingDays.value = 0;
  selectedPeriodsForChart.value = [];
  
  klineChartData.value = {
    dates: [],
    klineData: [],
    maData: {},
    title: '',
    periods: []
  };
  
  showKlineChart.value = false;
  queryPerformed.value = false;
  errorMsg.value = '';

  // 销毁图表实例
  if (statsChartInstance) {
    statsChartInstance.dispose();
    statsChartInstance = null;
  }
  if (periodComparisonChartInstance) {
    periodComparisonChartInstance.dispose();
    periodComparisonChartInstance = null;
  }
  if (multiPeriodChartInstance) {
    multiPeriodChartInstance.dispose();
    multiPeriodChartInstance = null;
  }
};

// 初始化或更新统计图表
const initOrUpdateStatsChart = () => {
  if (!statsChartRef.value || !currentPeriodResult.value || !currentPeriodResult.value.stats) {
    return;
  }
  
  if (!klineChartData.value.dates || klineChartData.value.dates.length === 0) {
    return;
  }

  if (!statsChartInstance) {
    statsChartInstance = echarts.init(statsChartRef.value);
  }

  // 创建从group_end_date到统计对象的映射
  const statsMap = new Map();
  currentPeriodResult.value.stats.forEach(stat => {
    statsMap.set(stat.group_end_date, stat);
  });

  // 准备数据系列
  const xAxisDates = klineChartData.value.dates;
  const meanData = [];
  const customSeriesData = []; // 用于自定义系列，包含均值和标准差

  // 将统计数据映射到K线日期上
  xAxisDates.forEach(date => {
    const stat = statsMap.get(date);
    if (stat) {
      meanData.push([date, stat.mean]);
      customSeriesData.push([date, stat.mean, stat.std_dev]);
    } else {
      meanData.push([date, null]);
      customSeriesData.push([date, null, null]);
    }
  });

  // 过滤有效数据点
  const validMeanDataPoints = meanData.filter(d => d[1] !== null && isFinite(d[1]));
  const validCustomDataPoints = customSeriesData.filter(d => 
    d[1] !== null && isFinite(d[1]) && d[2] !== null && isFinite(d[2])
  );

  // 设置图表选项
  const option = {
    title: {
      text: `${currentPeriodResult.value.period}天周期涨跌幅均值和标准差`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function(params) {
        const date = params[0].value[0];
        let tooltip = `<div><strong>${date}</strong></div>`;
        
        for (const param of params) {
          if (param.seriesName === '均值') {
            const mean = param.value[1];
            if (mean !== null && isFinite(mean)) {
              tooltip += `<div>${param.marker}均值: ${mean.toFixed(2) }%</div>`;
            }
          } else if (param.seriesName === '均值±标准差') {
            const data = param.value;
            if (data[1] !== null && isFinite(data[1]) && data[2] !== null && isFinite(data[2])) {
              const mean = data[1];
              const stdDev = data[2];
              tooltip += `<div>${param.marker}标准差: ${stdDev.toFixed(4) }</div>`;
              tooltip += `<div>${param.marker}范围: ${(mean - stdDev).toFixed(2) }% 至 ${(mean + stdDev).toFixed(2) }%</div>`;
            }
          }
        }
        
        return tooltip;
      }
    },
    legend: {
      data: ['均值', '均值±标准差'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisDates,
      axisLabel: {
        formatter: function(value) {
          return value;
        },
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100
      }
    ],
    series: [
      {
        name: '均值',
        type: 'line',
        data: meanData,
        symbolSize: 6,
        itemStyle: {
          color: '#5470c6'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0,0,0,0.3)'
          }
        }
      },
      {
        name: '均值±标准差',
        type: 'custom',
        renderItem: function(params, api) {
          const xValue = api.value(0);
          const yValue = api.value(1);
          const stdDev = api.value(2);
          
          if (yValue === null || !isFinite(yValue) || stdDev === null || !isFinite(stdDev)) {
            return;
          }
          
          const yUpper = yValue + stdDev;
          const yLower = yValue - stdDev;
          
          const xPos = api.coord([xValue, yValue])[0];
          const yPos = api.coord([xValue, yValue])[1];
          const yUpperPos = api.coord([xValue, yUpper])[1];
          const yLowerPos = api.coord([xValue, yLower])[1];
          
          const rectWidth = 10;
          const halfRectWidth = rectWidth / 2;
          
          // 绘制误差棒
          const errorBar = {
            type: 'group',
            children: [
              // 垂直线
              {
                type: 'line',
                shape: {
                  x1: xPos,
                  y1: yUpperPos,
                  x2: xPos,
                  y2: yLowerPos
                },
                style: {
                  stroke: '#5470c6',
                  opacity: 0.5
                }
              },
              // 上端横线
              {
                type: 'line',
                shape: {
                  x1: xPos - halfRectWidth,
                  y1: yUpperPos,
                  x2: xPos + halfRectWidth,
                  y2: yUpperPos
                },
                style: {
                  stroke: '#5470c6',
                  opacity: 0.5
                }
              },
              // 下端横线
              {
                type: 'line',
                shape: {
                  x1: xPos - halfRectWidth,
                  y1: yLowerPos,
                  x2: xPos + halfRectWidth,
                  y2: yLowerPos
                },
                style: {
                  stroke: '#5470c6',
                  opacity: 0.5
                }
              }
            ]
          };
          
          return errorBar;
        },
        data: customSeriesData,
        z: 100
      }
    ]
  };

  statsChartInstance.setOption(option);
  
  // 如果K线图实例存在，尝试连接数据缩放联动
  if (klineChartRef.value && klineChartRef.value.chartInstance) {
    echarts.connect([klineChartRef.value.chartInstance, statsChartInstance]);
  }
};

// 初始化或更新周期对比图表
const initOrUpdatePeriodComparisonChart = () => {
  if (!periodComparisonChartRef.value || periodResults.value.length <= 1) {
    return;
  }

  if (!periodComparisonChartInstance) {
    periodComparisonChartInstance = echarts.init(periodComparisonChartRef.value);
  }

  // 准备各周期的均值和标准差数据
  const periodsData = [];
  const meanValues = [];
  const stdDevValues = [];

  periodResults.value.forEach(result => {
    // 计算该周期所有统计点的均值的平均值和标准差的平均值
    let totalMean = 0;
    let totalStdDev = 0;
    let validStats = 0;

    result.stats.forEach(stat => {
      if (stat.mean !== null && isFinite(stat.mean) && 
          stat.std_dev !== null && isFinite(stat.std_dev)) {
        totalMean += stat.mean;
        totalStdDev += stat.std_dev;
        validStats++;
      }
    });

    if (validStats > 0) {
      const avgMean = totalMean / validStats;
      const avgStdDev = totalStdDev / validStats;
      
      periodsData.push(result.period);
      meanValues.push(avgMean);
      stdDevValues.push(avgStdDev);
    }
  });

  // 设置图表选项
  const option = {
    title: {
      text: '各周期均值和标准差对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const period = params[0].axisValue;
        let tooltip = `<div><strong>${period}天周期</strong></div>`;
        
        for (const param of params) {
          if (param.seriesName === '均值') {
            tooltip += `<div>${param.marker}均值: ${param.value.toFixed(2) }%</div>`;
          } else if (param.seriesName === '标准差') {
            tooltip += `<div>${param.marker}标准差: ${param.value.toFixed(4) }</div>`;
          }
        }
        
        return tooltip;
      }
    },
    legend: {
      data: ['均值', '标准差'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: periodsData,
      name: '周期(天)',
      nameLocation: 'end',
      nameGap: 15
    },
    yAxis: [
      {
        type: 'value',
        name: '均值(%)',
        position: 'left',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      {
        type: 'value',
        name: '标准差',
        position: 'right',
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '均值',
        type: 'bar',
        data: meanValues,
        itemStyle: {
          color: '#5470c6'
        },
        yAxisIndex: 0
      },
      {
        name: '标准差',
        type: 'line',
        data: stdDevValues,
        itemStyle: {
          color: '#91cc75'
        },
        yAxisIndex: 1,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8
      }
    ]
  };

  periodComparisonChartInstance.setOption(option);
};

// 初始化或更新多周期数据对比图表
const initOrUpdateMultiPeriodChart = () => {
  if (!multiPeriodChartRef.value || periodResults.value.length === 0 || selectedPeriodsForChart.value.length === 0) {
    return;
  }

  if (!klineChartData.value.dates || klineChartData.value.dates.length === 0) {
    return;
  }

  if (!multiPeriodChartInstance) {
    multiPeriodChartInstance = echarts.init(multiPeriodChartRef.value);
  }

  const xAxisDates = klineChartData.value.dates;
  const series = [];
  const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];
  
  // 为每个选中的周期创建数据系列
  selectedPeriodsForChart.value.forEach((periodIndex, idx) => {
    const result = periodResults.value[periodIndex];
    if (!result || !result.stats) return;
    
    const color = colors[idx % colors.length];
    const periodMap = new Map();
    
    // 创建日期到统计数据的映射
    result.stats.forEach(stat => {
      periodMap.set(stat.group_end_date, stat);
    });
    
    // 准备均值数据
    const meanData = [];
    
    // 如果显示标准差范围，准备上下边界数据
    const upperData = [];
    const lowerData = [];
    
    // 将统计数据映射到日期轴上
    xAxisDates.forEach(date => {
      const stat = periodMap.get(date);
      if (stat && stat.mean !== null && isFinite(stat.mean)) {
        meanData.push([date, stat.mean]);
        
        if (showStdDev.value && stat.std_dev !== null && isFinite(stat.std_dev)) {
          upperData.push([date, stat.mean + stat.std_dev]);
          lowerData.push([date, stat.mean - stat.std_dev]);
        } else {
          upperData.push([date, null]);
          lowerData.push([date, null]);
        }
      } else {
        meanData.push([date, null]);
        upperData.push([date, null]);
        lowerData.push([date, null]);
      }
    });
    
    // 添加均值线
    series.push({
      name: `${result.period}天周期均值`,
      type: 'line',
      data: meanData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      sampling: 'average',
      itemStyle: {
        color: color
      },
      emphasis: {
        focus: 'series'
      },
      z: 5
    });
    
    // 如果需要显示标准差范围，添加误差通道
    if (showStdDev.value) {
      // 创建误差通道的填充数据
      // 将上边界和下边界数据合并，形成一个封闭的区域
      const areaData = [];

      // 先添加所有的下边界点
      lowerData.forEach(point => {
        if (point[1] !== null && isFinite(point[1])) {
          areaData.push(point);
        }
      });

      // 然后反向添加所有的上边界点，形成封闭区域
      for (let i = upperData.length - 1; i >= 0; i--) {
        const point = upperData[i];
        if (point[1] !== null && isFinite(point[1])) {
          areaData.push(point);
        }
      }

      // 使用polygon类型绘制误差通道
      series.push({
        name: `${result.period}天周期误差通道`,
        type: 'custom',
        data: [[0, 0]], // 占位数据
        renderItem: function(params, api) {
          // 将所有有效的上下边界点转换为像素坐标
          const lowerPoints = [];
          const upperPoints = [];

          lowerData.forEach(point => {
            if (point[1] !== null && isFinite(point[1])) {
              const coord = api.coord([point[0], point[1]]);
              if (coord) lowerPoints.push(coord);
            }
          });

          upperData.forEach(point => {
            if (point[1] !== null && isFinite(point[1])) {
              const coord = api.coord([point[0], point[1]]);
              if (coord) upperPoints.push(coord);
            }
          });

          if (lowerPoints.length === 0 || upperPoints.length === 0) {
            return;
          }

          // 构建多边形路径：下边界 + 反向上边界
          const polygonPoints = [...lowerPoints];
          for (let i = upperPoints.length - 1; i >= 0; i--) {
            polygonPoints.push(upperPoints[i]);
          }

          return {
            type: 'polygon',
            shape: {
              points: polygonPoints
            },
            style: {
              fill: echarts.color.lerp(0.2, [color, '#fff']),
              opacity: 0.3,
              stroke: 'transparent'
            },
            silent: true,
            z: 1
          };
        },
        silent: true,
        z: 1
      });

      // 下边界线（均值-标准差）
      series.push({
        name: `${result.period}天周期下边界`,
        type: 'line',
        data: lowerData,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: color,
          width: 1,
          type: 'dashed',
          opacity: 0.7
        },
        silent: true,
        z: 3
      });

      // 上边界线（均值+标准差）
      series.push({
        name: `${result.period}天周期上边界`,
        type: 'line',
        data: upperData,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: color,
          width: 1,
          type: 'dashed',
          opacity: 0.7
        },
        silent: true,
        z: 3
      });
    }
  });
  
  // 设置图表选项
  const option = {
    title: {
      text: '多周期涨跌幅均值与标准差对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function(params) {
        const date = params[0].value[0];
        let tooltip = `<div><strong>${date}</strong></div>`;
        
        // 只显示均值系列的数据
        params.forEach(param => {
          // 如果是均值系列（不包含边界线）
          if (param.seriesName.includes('均值') && !param.seriesName.includes('边界')) {
            const value = param.value[1];
            if (value !== null && isFinite(value)) {
              tooltip += `<div>${param.marker}${param.seriesName}: ${value.toFixed(2)}%</div>`;

              // 查找对应的周期结果
              const periodIndex = selectedPeriodsForChart.value.find(
                idx => periodResults.value[idx].period === parseInt(param.seriesName.split('天')[0])
              );

              if (periodIndex !== undefined) {
                const result = periodResults.value[periodIndex];
                // 查找该日期对应的统计数据
                const stat = result.stats.find(s => s.group_end_date === date);

                if (stat && stat.std_dev !== null && isFinite(stat.std_dev)) {
                  const upperBound = (value + stat.std_dev).toFixed(2);
                  const lowerBound = (value - stat.std_dev).toFixed(2);
                  tooltip += `<div style="padding-left: 20px; color: #666;">标准差: ±${stat.std_dev.toFixed(4)}</div>`;
                  tooltip += `<div style="padding-left: 20px; color: #666;">误差通道: ${lowerBound}% ~ ${upperBound}%</div>`;
                }
              }
            }
          }
        });
        
        return tooltip;
      }
    },
    legend: {
      data: series.filter(s => !s.silent && s.name.includes('均值')).map(s => s.name),
      top: 30,
      type: 'scroll',
      pageIconSize: 12,
      pageButtonPosition: 'end'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisDates,
      axisLabel: {
        formatter: function(value) {
          return value;
        },
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100
      }
    ],
    series: series
  };

  multiPeriodChartInstance.setOption(option, true); // true表示不合并，完全替换

  // 如果K线图实例存在，尝试连接数据缩放联动
  if (klineChartRef.value && klineChartRef.value.chartInstance) {
    echarts.connect([klineChartRef.value.chartInstance, multiPeriodChartInstance]);
  }
};

// 图表联动函数
const connectCharts = () => {
  if (klineChartRef.value && klineChartRef.value.chartInstance && statsChartInstance) {
    echarts.connect([klineChartRef.value.chartInstance, statsChartInstance]);
  }
};

// 统一响应窗口大小变化的处理
window.addEventListener('resize', () => {
  if (statsChartInstance) {
    statsChartInstance.resize();
  }
  if (periodComparisonChartInstance) {
    periodComparisonChartInstance.resize();
  }
  if (multiPeriodChartInstance) {
    multiPeriodChartInstance.resize();
  }
});
</script>

<style scoped>
.page-container {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.query-form {
  margin: 20px;
  display: flex;
  flex-wrap: wrap;
}

.query-form .el-form-item {
  margin-right: 20px;
}

.loading-container {
  padding: 20px;
}

.results-container {
  padding: 20px;
}

.kline-chart-container,
.stats-chart-container {
  margin-top: 20px;
  width: 100%;
}

.positive-text {
  color: #67c23a;
  font-weight: bold;
}

.negative-text {
  color: #f56c6c;
  font-weight: bold;
}
</style> 