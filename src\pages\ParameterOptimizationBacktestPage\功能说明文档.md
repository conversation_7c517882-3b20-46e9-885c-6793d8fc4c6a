# 参数优化回测页面功能说明文档

## 页面概述

参数优化回测页面（ParameterOptimizationBacktestPage）是一个专门用于对标准差交叉策略进行参数优化的功能模块。该页面通过设定短周期和长周期的参数范围和步长，自动遍历所有有效的参数组合，对每个组合进行回测分析，帮助用户找到最优的交易参数。

## 核心功能

### 1. 参数配置功能

页面提供了完整的参数配置表单，包括：

#### 基础参数
- **股票代码**：支持多个股票代码输入，用逗号、分号或换行分隔
- **开始日期**：回测的起始日期
- **结束日期**：回测的结束日期
- **初始资金**：回测的初始投资金额
- **每次交易股数**：每次买入或卖出的股票数量

#### 参数优化配置
- **短周期最小值**：短周期参数的最小值（例如：2）
- **短周期最大值**：短周期参数的最大值（例如：10）
- **短周期步长**：短周期参数的递增步长（例如：2）
- **长周期最小值**：长周期参数的最小值（例如：10）
- **长周期最大值**：长周期参数的最大值（例如：50）
- **长周期步长**：长周期参数的递增步长（例如：10）

### 2. 智能参数组合生成

系统会根据用户设定的参数范围和步长，自动生成所有有效的参数组合：

- **约束条件**：自动确保长周期 > 短周期，过滤掉无效组合
- **组合预览**：实时显示将要测试的参数组合数量
- **批量处理**：支持多个股票代码与多个参数组合的笛卡尔积

#### 示例
如果设置：
- 短周期：[2-10]，步长2 → 生成：2, 4, 6, 8, 10
- 长周期：[10-50]，步长10 → 生成：10, 20, 30, 40, 50
- 有效组合：(2,10), (2,20), (2,30), (2,40), (2,50), (4,10), (4,20), ..., (10,20), (10,30), (10,40), (10,50)
- 总计：25个有效参数组合

### 3. 批量回测执行

系统会对所有有效的参数组合进行批量回测：

- **并发处理**：逐个处理每个参数组合，避免服务器过载
- **进度跟踪**：实时显示当前处理的股票代码和参数组合
- **错误处理**：单个组合失败不会影响其他组合的执行
- **结果收集**：收集所有成功执行的回测结果

### 4. 结果展示与分析

#### 优化结果表格
- **多维度排序**：支持按总收益率、年化收益率、夏普比率、最大回撤、胜率等指标排序
- **升序/降序**：可选择升序或降序排列
- **详细信息**：显示每个参数组合的完整回测指标

#### 关键指标包括：
- 股票代码
- 短周期参数
- 长周期参数
- 总收益率
- 年化收益率
- 最大回撤
- 夏普比率
- 胜率
- 交易次数
- 最终价值

### 5. 详细结果查看

点击任意结果行可查看该参数组合的详细回测信息：

- **核心指标展示**：以描述列表形式展示所有关键指标
- **投资组合价值图表**：显示该参数组合下的资产价值变化曲线
- **交互式图表**：支持缩放、平移等操作
- **数据导出**：支持将优化结果导出为CSV文件

### 6. 筛选与排序功能

- **智能排序**：默认按总收益率降序排列，快速找到最优参数
- **多指标排序**：可切换不同的排序指标
- **成功率统计**：显示成功执行的参数组合数量

### 7. 集成分析功能

- **一键分析**：点击"分析"按钮可在新标签页中打开该股票的详细分析页面
- **智能参数传递**：自动传递股票代码、短周期、长周期参数到分析页面
- **周期范围映射**：将优化结果的短周期和长周期映射为批量分析的最小周期和最大周期
- **智能步长计算**：根据周期范围自动计算合适的步长，最多分5个区间进行分析
- **无缝衔接**：与现有的批量周期涨跌统计功能完美集成

## 使用场景

### 1. 策略参数优化
- 为标准差交叉策略寻找最优的短周期和长周期参数
- 对比不同参数组合在相同时间段的表现
- 发现参数敏感性和稳定性

### 2. 多股票对比
- 同时测试多个股票在相同参数下的表现
- 找到适合特定股票的最优参数组合
- 识别参数普适性

### 3. 风险评估
- 通过最大回撤指标评估不同参数的风险水平
- 通过夏普比率评估风险调整后的收益
- 通过胜率评估策略的稳定性

## 技术特点

### 1. 高效的批量处理
- 异步处理机制，避免界面阻塞
- 详细的进度反馈
- 错误隔离，单点失败不影响整体

### 2. 智能的参数验证
- 自动验证参数范围的合理性
- 确保长周期大于短周期的约束
- 实时计算和显示参数组合数量

### 3. 丰富的可视化
- 交互式图表展示
- 多维度数据排序
- 直观的结果对比

### 4. 无缝的功能集成
- 与现有回测API完全兼容
- 复用现有的图表组件
- 保持一致的用户体验

## 数据流程

1. **参数配置**：用户设置股票代码、时间范围和参数优化配置
2. **组合生成**：系统生成所有有效的参数组合
3. **批量回测**：逐个调用回测API，收集结果
4. **结果排序**：按用户选择的指标对结果进行排序
5. **详情展示**：用户可查看任意参数组合的详细结果
6. **数据导出**：支持将结果导出为CSV格式

## 接口调用

### 回测API接口
**接口路径**: `/api/period_change_pct_stats/backtest/analyze/{stock_code}`

**请求方式**: GET

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|-------|-----|------|------|-------|
| start_date | String | 是 | 开始日期 | 2023-01-01 |
| end_date | String | 是 | 结束日期 | 2023-12-31 |
| short_period | Number | 是 | 短周期参数 | 2 |
| long_period | Number | 是 | 长周期参数 | 20 |
| initial_capital | Number | 是 | 初始资金 | 100000 |
| shares_per_trade | Number | 是 | 每次交易股数 | 100 |

该页面复用了现有的标准差交叉回测API，确保了数据的一致性和可靠性。

## 优势特点

1. **自动化程度高**：一次设置，自动遍历所有参数组合
2. **结果对比直观**：表格化展示，支持多维度排序
3. **操作简单易用**：界面友好，参数设置清晰
4. **功能集成完善**：与现有功能无缝衔接，智能参数传递
5. **扩展性良好**：易于添加新的优化指标和功能

## 最新更新

### 参数传递优化 (2024-06-11)

修复了从参数优化回测页面跳转到批量周期涨跌统计页面时参数传递不正确的问题：

**问题描述**：
- 用户在参数优化页面选择了短周期8、长周期50的组合
- 点击"分析"按钮跳转到批量周期涨跌统计页面
- 但页面显示的是默认参数（最小周期2、最大周期40），而不是期望的8-50范围

**解决方案**：
1. **修改跳转逻辑**：在 `analyzeOptimizationStock` 函数中添加周期参数传递
2. **智能参数映射**：
   - `minPeriod` = 短周期参数
   - `maxPeriod` = 长周期参数
   - `periodStep` = 智能计算步长（最多分5个区间）
3. **增强接收逻辑**：批量周期涨跌统计页面现在能接收并使用这些路由参数

**修复效果**：
- 现在点击"分析"按钮会正确传递周期参数
- 跳转后的页面会自动填入对应的周期范围
- 提供更精确的分析范围，提升用户体验

**示例**：
- 选择短周期8、长周期50的组合点击分析
- 跳转URL：`/batch-period-change-stats?stockCode=000571&minPeriod=8&maxPeriod=50&periodStep=8&autoAnalyze=true`
- 页面自动填入：股票代码000571，最小周期8，最大周期50，步长8
- 自动执行分析，显示8-50周期范围内的涨跌统计
